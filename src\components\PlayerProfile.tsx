import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, MapPin, Users, Trophy, Clock } from "lucide-react";
import playerPhoto from "@/assets/player-photo.jpg";

const PlayerProfile = () => {
  return (
    <div className="container mx-auto px-4 py-6">
      {/* Player Header */}
      <div className="bg-tm-white rounded-lg shadow-sm mb-6">
        <div className="p-4 md:p-6">
          {/* Mobile Layout */}
          <div className="block md:hidden">
            <div className="flex items-start gap-4 mb-4">
              {/* Player Photo */}
              <div className="relative flex-shrink-0">
                <img
                  src={playerPhoto}
                  alt="Wasiu Alalade"
                  className="w-20 h-24 sm:w-24 sm:h-30 object-cover rounded-lg border-2 border-tm-gray"
                />
                <div className="absolute -top-2 -left-2 bg-tm-blue text-tm-white rounded-full w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center font-bold text-xs sm:text-sm">
                  17
                </div>
              </div>

              {/* Player Basic Info */}
              <div className="flex-1 min-w-0">
                <h1 className="text-xl sm:text-2xl font-bold text-tm-navy mb-2">#17 Wasiu Alalade</h1>
                <div className="flex items-center gap-2 mb-2">
                  <span className="text-xs text-tm-dark-gray">Position:</span>
                  <Badge variant="secondary" className="bg-tm-blue text-tm-white text-xs">Centre-Forward</Badge>
                </div>
                <div className="space-y-1 text-xs text-tm-dark-gray">
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>Jul 6, 1999 (26)</span>
                  </div>
                  <div className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    <span>Crown City</span>
                  </div>
                  <div>Height: 1,83 m</div>
                </div>
              </div>
            </div>

            {/* Team Info - Mobile */}
            <div className="bg-tm-gray rounded-lg p-3 mb-4">
              <div className="flex items-center gap-3 mb-2">
                <img src="/Crown_F.C_Nigeria_logo.svg.png" alt="Crown FC" className="w-8 h-8" />
                <div>
                  <h3 className="font-bold text-tm-navy text-sm">Crown FC</h3>
                  <div className="text-xs text-tm-dark-gray">Premier League</div>
                </div>
              </div>
              <div className="text-xs text-tm-dark-gray space-y-1">
                <div>League level: First Tier</div>
                <div>Joined: Feb 15, 2024</div>
              </div>
            </div>

            {/* Stats Row - Mobile */}
            <div className="grid grid-cols-3 gap-2 text-center">
              <div className="bg-tm-gray rounded p-2">
                <div className="text-xs text-tm-dark-gray mb-1">Youth Career</div>
                <div className="font-bold text-tm-navy text-xs">Crown Academy</div>
              </div>
              <div className="bg-tm-gray rounded p-2">
                <div className="text-xs text-tm-dark-gray mb-1">Goals/Assists</div>
                <div className="font-bold text-tm-navy text-sm">8 / 3</div>
              </div>
              <div className="bg-tm-gray rounded p-2">
                <div className="text-xs text-tm-dark-gray mb-1">Foot</div>
                <div className="font-bold text-tm-navy text-xs">Right</div>
              </div>
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden md:flex items-start gap-6">
            {/* Player Photo */}
            <div className="relative">
              <img
                src={playerPhoto}
                alt="Wasiu Alalade"
                className="w-32 h-40 object-cover rounded-lg border-2 border-tm-gray"
              />
              <div className="absolute -top-2 -left-2 bg-tm-blue text-tm-white rounded-full w-8 h-8 flex items-center justify-center font-bold">
                17
              </div>
            </div>

            {/* Player Info */}
            <div className="flex-1">
              <div className="flex items-start justify-between mb-4">
                <div>
                  <h1 className="text-3xl font-bold text-tm-navy mb-2">#17 Wasiu Alalade</h1>
                  <div className="flex items-center gap-4 text-sm text-tm-dark-gray">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>Jul 6, 1999 (26)</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <MapPin className="h-4 w-4" />
                      <span>Crown City</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <span>Height: 1,83 m</span>
                    </div>
                  </div>
                  <div className="flex items-center gap-2 mt-2">
                    <span className="text-sm text-tm-dark-gray">Position:</span>
                    <Badge variant="secondary" className="bg-tm-blue text-tm-white">Centre-Forward</Badge>
                  </div>
                </div>

                {/* Team Info */}
                <div className="bg-tm-gray rounded-lg p-4 min-w-[250px]">
                  <div className="flex items-center gap-3 mb-2">
                    <img src="/Crown_F.C_Nigeria_logo.svg.png" alt="Crown FC" className="w-12 h-12" />
                    <div>
                      <h3 className="font-bold text-tm-navy">Crown FC</h3>
                      <div className="flex items-center gap-1 text-sm text-tm-dark-gray">
                        <span>Premier League</span>
                      </div>
                    </div>
                  </div>
                  <div className="text-xs text-tm-dark-gray">
                    <div>League level: First Tier</div>
                    <div>Joined: Feb 15, 2024</div>
                    <div>Contract expires: -</div>
                  </div>
                </div>
              </div>

              {/* Stats Row */}
              <div className="grid grid-cols-3 gap-4 text-center">
                <div className="bg-tm-gray rounded p-3">
                  <div className="text-sm text-tm-dark-gray mb-1">Youth Career</div>
                  <div className="font-bold text-tm-navy">Crown Academy</div>
                </div>
                <div className="bg-tm-gray rounded p-3">
                  <div className="text-sm text-tm-dark-gray mb-1">Goals/Assists</div>
                  <div className="font-bold text-tm-navy">8 / 3</div>
                </div>
                <div className="bg-tm-gray rounded p-3">
                  <div className="text-sm text-tm-dark-gray mb-1">Foot</div>
                  <div className="font-bold text-tm-navy">Right</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Grid */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 md:gap-6">
        {/* Player Data Card */}
        <Card className="xl:col-span-2">
          <CardHeader className="bg-tm-navy text-tm-white rounded-t-lg">
            <h2 className="font-bold text-sm md:text-base">PLAYER DATA</h2>
          </CardHeader>
          <CardContent className="p-0">
            {/* Mobile Layout - Single Column */}
            <div className="block md:hidden">
              <div className="space-y-3 p-4">
                <div className="border-b border-tm-gray pb-3">
                  <div className="text-xs text-tm-dark-gray mb-1">Date of birth/Age:</div>
                  <div className="font-medium text-tm-blue text-sm">Jul 6, 1999 (26)</div>
                </div>
                <div className="border-b border-tm-gray pb-3">
                  <div className="text-xs text-tm-dark-gray mb-1">Main position</div>
                  <div className="font-medium text-sm">Centre-Forward</div>
                </div>
                <div className="border-b border-tm-gray pb-3">
                  <div className="text-xs text-tm-dark-gray mb-1">Place of birth:</div>
                  <div className="font-medium text-sm">Crown City</div>
                </div>
                <div className="border-b border-tm-gray pb-3">
                  <div className="text-xs text-tm-dark-gray mb-1">Other position:</div>
                  <div className="font-medium text-sm">Left Winger, Right Winger</div>
                </div>
                <div className="border-b border-tm-gray pb-3">
                  <div className="text-xs text-tm-dark-gray mb-1">Height:</div>
                  <div className="font-medium text-sm">1,83 m</div>
                </div>
                <div className="border-b border-tm-gray pb-3">
                  <div className="text-xs text-tm-dark-gray mb-1">Foot:</div>
                  <div className="font-medium text-sm">Right</div>
                </div>
                <div className="border-b border-tm-gray pb-3">
                  <div className="text-xs text-tm-dark-gray mb-1">Hometown:</div>
                  <div className="font-medium text-sm">Crown City</div>
                </div>
                <div>
                  <div className="text-xs text-tm-dark-gray mb-1">Player agent:</div>
                  <div className="font-medium text-tm-blue text-sm">Crown Sports Management</div>
                </div>
              </div>
            </div>

            {/* Desktop Layout - Two Columns */}
            <div className="hidden md:grid md:grid-cols-2 gap-0">
              <div className="p-3 md:p-4 border-b border-r border-tm-gray">
                <div className="text-sm text-tm-dark-gray mb-1">Date of birth/Age:</div>
                <div className="font-medium text-tm-blue">Jul 6, 1999 (26)</div>
              </div>
              <div className="p-3 md:p-4 border-b border-tm-gray">
                <div className="text-sm text-tm-dark-gray mb-1">Main position</div>
                <div className="font-medium">Centre-Forward</div>
              </div>
              <div className="p-3 md:p-4 border-b border-r border-tm-gray">
                <div className="text-sm text-tm-dark-gray mb-1">Place of birth:</div>
                <div className="flex items-center gap-1">
                  <span className="font-medium">Crown City</span>
                </div>
              </div>
              <div className="p-3 md:p-4 border-b border-tm-gray">
                <div className="text-sm text-tm-dark-gray mb-1">Other position:</div>
                <div className="font-medium">Left Winger, Right Winger</div>
              </div>
              <div className="p-3 md:p-4 border-b border-r border-tm-gray">
                <div className="text-sm text-tm-dark-gray mb-1">Height:</div>
                <div className="font-medium">1,83 m</div>
              </div>
              <div className="p-3 md:p-4 border-b border-tm-gray">
                <div className="text-sm text-tm-dark-gray mb-1">Foot:</div>
                <div className="font-medium">Right</div>
              </div>
              <div className="p-3 md:p-4 border-r border-tm-gray">
                <div className="text-sm text-tm-dark-gray mb-1">Hometown:</div>
                <div className="flex items-center gap-1">
                  <span className="font-medium">Crown City</span>
                </div>
              </div>
              <div className="p-3 md:p-4">
                <div className="text-sm text-tm-dark-gray mb-1">Player agent:</div>
                <div className="font-medium text-tm-blue">Crown Sports Management</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Matches Card */}
        <Card>
          <CardHeader className="bg-tm-navy text-tm-white rounded-t-lg">
            <h2 className="font-bold text-sm md:text-base">MATCHES</h2>
          </CardHeader>
          <CardContent className="p-3 md:p-4">
              <div className="space-y-3">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-2 border-b border-tm-gray gap-2">
                  <div>
                    <div className="font-medium text-xs sm:text-sm">Premier League - 25. Matchday</div>
                    <div className="text-xs text-tm-dark-gray">Sunday, 01/20/2025 - 3:00 PM</div>
                  </div>
                  <div className="text-left sm:text-right">
                    <div className="text-sm font-bold">vs Thunder FC</div>
                  </div>
                </div>
              </div>
          </CardContent>
        </Card>
      </div>

      {/* Career Stats */}
      <Card className="mt-4 md:mt-6">
        <CardHeader className="bg-tm-navy text-tm-white rounded-t-lg">
          <h2 className="font-bold text-sm md:text-base">CAREER STATS</h2>
        </CardHeader>
        <CardContent className="p-0">
          {/* Mobile Layout - Card Style */}
          <div className="block md:hidden p-4">
            <div className="bg-tm-gray rounded-lg p-3">
              <h3 className="font-bold text-tm-navy mb-3 text-sm">Premier League</h3>
              <div className="grid grid-cols-2 gap-3 text-xs">
                <div className="text-center">
                  <div className="text-tm-dark-gray mb-1">Matches</div>
                  <div className="font-bold text-tm-navy">15</div>
                </div>
                <div className="text-center">
                  <div className="text-tm-dark-gray mb-1">Goals</div>
                  <div className="font-bold text-tm-navy">8</div>
                </div>
                <div className="text-center">
                  <div className="text-tm-dark-gray mb-1">Assists</div>
                  <div className="font-bold text-tm-navy">3</div>
                </div>
                <div className="text-center">
                  <div className="text-tm-dark-gray mb-1">Yellow cards</div>
                  <div className="font-bold text-tm-navy">2</div>
                </div>
                <div className="text-center">
                  <div className="text-tm-dark-gray mb-1">Red cards</div>
                  <div className="font-bold text-tm-navy">0</div>
                </div>
                <div className="text-center">
                  <div className="text-tm-dark-gray mb-1">Minutes</div>
                  <div className="font-bold text-tm-navy">1,245</div>
                </div>
              </div>
            </div>
          </div>

          {/* Desktop Layout - Table */}
          <div className="hidden md:block overflow-x-auto">
            <table className="w-full">
              <thead className="bg-tm-gray">
                <tr>
                  <th className="text-left p-3 text-sm font-medium">Competition</th>
                  <th className="text-center p-3 text-sm font-medium">Matches</th>
                  <th className="text-center p-3 text-sm font-medium">Goals</th>
                  <th className="text-center p-3 text-sm font-medium">Assists</th>
                  <th className="text-center p-3 text-sm font-medium">Yellow cards</th>
                  <th className="text-center p-3 text-sm font-medium">Red cards</th>
                  <th className="text-center p-3 text-sm font-medium">Minutes played</th>
                </tr>
              </thead>
              <tbody>
                <tr className="border-b border-tm-gray hover:bg-tm-gray/50">
                  <td className="p-3">Premier League</td>
                  <td className="p-3 text-center">15</td>
                  <td className="p-3 text-center">8</td>
                  <td className="p-3 text-center">3</td>
                  <td className="p-3 text-center">2</td>
                  <td className="p-3 text-center">0</td>
                  <td className="p-3 text-center">1,245</td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default PlayerProfile;