# Crown Football Club Ogbomoso - Official Website

## Project Overview

This is the official website for Crown Football Club Ogbomoso, a Nigerian football club founded in 1994 by Rev. Prof. <PERSON>. The club is based in Ogbomoso, Oyo State, and competes in the Nigeria National League.

## Getting Started

To run this project locally, you need to have Node.js & npm installed - [install with nvm](https://github.com/nvm-sh/nvm#installing-and-updating)

Follow these steps:

```sh
# Step 1: Clone the repository
git clone <YOUR_GIT_URL>

# Step 2: Navigate to the project directory
cd crown_fc

# Step 3: Install the necessary dependencies
npm i

# Step 4: Start the development server
npm run dev
```

## Development

You can edit this code using your preferred IDE. The project supports hot reloading for a smooth development experience.

## Technologies Used

This project is built with modern web technologies:

- **Vite** - Fast build tool and development server
- **TypeScript** - Type-safe JavaScript
- **React** - UI library with hooks and modern patterns
- **shadcn/ui** - Beautiful and accessible UI components
- **Tailwind CSS** - Utility-first CSS framework
- **Framer Motion** - Animation library for smooth interactions
- **React Router** - Client-side routing

## Features

- Responsive design for mobile and desktop
- Interactive animations and transitions
- Loading screen with club logo
- News and fixtures sections
- Player profiles and gallery
- Contact information and social media links

## Club Information

- **Founded**: 1994 by Rev. Prof. Yusuf Ameh Obaje
- **Location**: Ogbomoso, Oyo State, Nigeria
- **Stadium**: Soun Stadium Ogbomosho (Capacity: 7,200)
- **League**: Nigeria National League
- **Main Rivals**: Shooting Stars F.C. of Ibadan

## Deployment

Build the project for production:

```sh
npm run build
```

The built files will be in the `dist` directory, ready for deployment to any static hosting service.
