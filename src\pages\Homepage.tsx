import Header from "../components/Header";
import Footer from "../components/Footer";
import { Card, CardContent, CardHeader } from "../components/ui/card";
import { Badge } from "../components/ui/badge";
import { Button } from "../components/ui/button";
import { Calendar, Play, ArrowRight, Ticket } from "lucide-react";
import { motion } from "framer-motion";

const Homepage = () => {

  // Main news articles
  const mainNews = [
    {
      id: 1,
      title: "ENYIMBA OVERPOWER RANGERS IN ENUGU THRILLER",
      category: "MATCH REPORT",
      date: "2 HOURS AGO",
      image: "/emmanuel-toyin-aderinola.jpg",
      excerpt: "Enyimba once again showed their authority in the oriental derby, signing and sealing away Enugu Rangers 2-1 at the Nnamdi Azikiwe Stadium on Sunday. The thriller was decided by the prowess of <PERSON><PERSON><PERSON> and <PERSON>.",
      content: "In a pulsating encounter that lived up to its billing, Enyimba FC proved decisive for the People's Elephant, who were ruthless both on and off the ball. The match was a testament to the quality of Nigerian football."
    }
  ];

  // Sidebar news
  const sidebarNews = [
    {
      id: 1,
      title: "YOUNG ELEPHANTS CHANGE ON WITH WIN AGAINST RIGHTFARM",
      category: "YOUTH TEAM",
      date: "4 HOURS AGO"
    },
    {
      id: 2,
      title: "CHAIRMAN'S THUNDERBOLT EXCITES FOOTBALL FANS OLD AND YOUNG",
      category: "CLUB NEWS",
      date: "6 HOURS AGO"
    },
    {
      id: 3,
      title: "ENYIMBA PREP PILLARS AS EZUMA GETS RIGHTFARM TEST LEAGUE FINISH",
      category: "MATCH PREVIEW",
      date: "1 DAY AGO"
    }
  ];

  // League table data
  const leagueTable = [
    { pos: 1, team: "Enyimba", mp: 15, w: 10, d: 3, l: 2, pts: 33 },
    { pos: 2, team: "Rangers", mp: 15, w: 9, d: 4, l: 2, pts: 31 },
    { pos: 3, team: "Plateau Utd", mp: 15, w: 8, d: 5, l: 2, pts: 29 },
    { pos: 4, team: "Rivers Utd", mp: 15, w: 7, d: 6, l: 2, pts: 27 },
    { pos: 5, team: "Akwa Utd", mp: 15, w: 7, d: 4, l: 4, pts: 25 },
    { pos: 6, team: "Heartland", mp: 15, w: 6, d: 5, l: 4, pts: 23 }
  ];

  return (
    <>
      <Header />
      <div className="min-h-screen bg-gradient-to-br from-blue-900 via-blue-800 to-blue-900">
      {/* Main Content Container */}
      <div className="container mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">

          {/* Main Content Area */}
          <div className="lg:col-span-2 space-y-8">

            {/* Featured Article */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
              className="relative"
            >
              <Card className="bg-blue-800/50 border-blue-600/30 overflow-hidden">
                <div className="relative">
                  <img
                    src={mainNews[0].image}
                    alt={mainNews[0].title}
                    className="w-full h-64 md:h-80 object-cover"
                  />
                  <div className="absolute top-4 left-4">
                    <Badge className="bg-yellow-500 text-black font-semibold">
                      {mainNews[0].category}
                    </Badge>
                  </div>
                </div>
                <CardContent className="p-6">
                  <div className="flex items-center gap-2 text-blue-300 text-sm mb-3">
                    <Calendar className="w-4 h-4" />
                    <span>{mainNews[0].date}</span>
                  </div>
                  <h1 className="text-2xl md:text-3xl font-bold text-white mb-4 leading-tight">
                    {mainNews[0].title}
                  </h1>
                  <p className="text-blue-100 mb-4 leading-relaxed">
                    {mainNews[0].excerpt}
                  </p>
                  <p className="text-blue-200 mb-6 leading-relaxed">
                    {mainNews[0].content}
                  </p>
                  <div className="flex flex-wrap gap-3">
                    <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                      <Play className="w-4 h-4 mr-2" />
                      WATCH HIGHLIGHTS
                    </Button>
                    <Button variant="outline" className="border-blue-400 text-blue-300 hover:bg-blue-700">
                      <ArrowRight className="w-4 h-4 mr-2" />
                      READ FULL STORY
                    </Button>
                    <Button variant="outline" className="border-green-400 text-green-300 hover:bg-green-700">
                      <Ticket className="w-4 h-4 mr-2" />
                      BOOK NEXT MATCH
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Comment Section */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <Card className="bg-blue-800/50 border-blue-600/30">
                <CardHeader>
                  <h3 className="text-xl font-bold text-white">LEAVE A REPLY</h3>
                </CardHeader>
                <CardContent>
                  <div className="bg-yellow-500/20 border border-yellow-500/30 rounded p-3 mb-4">
                    <p className="text-yellow-200 text-sm">
                      You must be logged in to post a comment.
                    </p>
                  </div>
                  <p className="text-blue-300 text-sm">
                    This site uses Akismet to reduce spam.
                    <a href="#" className="text-blue-400 hover:underline ml-1">
                      Learn how your comment data is processed
                    </a>
                    .
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">

            {/* Upcoming Section */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.3 }}
            >
              <Card className="bg-blue-800/50 border-blue-600/30">
                <CardHeader>
                  <h3 className="text-lg font-bold text-white bg-blue-700 -mx-6 -mt-6 px-6 py-3">
                    UPCOMING
                  </h3>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="text-center py-8">
                    <p className="text-blue-300">No upcoming events</p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* News Section */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.4 }}
            >
              <Card className="bg-blue-800/50 border-blue-600/30">
                <CardHeader>
                  <h3 className="text-lg font-bold text-white bg-blue-700 -mx-6 -mt-6 px-6 py-3">
                    NEWS
                  </h3>
                </CardHeader>
                <CardContent className="space-y-4">
                  {sidebarNews.map((news, index) => (
                    <motion.div
                      key={news.id}
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.4, delay: 0.5 + index * 0.1 }}
                      className="border-b border-blue-600/30 pb-4 last:border-b-0 last:pb-0"
                    >
                      <div className="flex items-start gap-2 mb-2">
                        <Badge variant="outline" className="border-yellow-500 text-yellow-400 text-xs">
                          {news.category}
                        </Badge>
                        <span className="text-blue-400 text-xs">{news.date}</span>
                      </div>
                      <h4 className="text-white font-semibold text-sm leading-tight hover:text-blue-300 cursor-pointer transition-colors">
                        {news.title}
                      </h4>
                    </motion.div>
                  ))}
                </CardContent>
              </Card>
            </motion.div>

            {/* League Table */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ duration: 0.6, delay: 0.5 }}
            >
              <Card className="bg-blue-800/50 border-blue-600/30">
                <CardHeader>
                  <h3 className="text-lg font-bold text-white bg-blue-700 -mx-6 -mt-6 px-6 py-3">
                    NPFL 2024/2025
                  </h3>
                </CardHeader>
                <CardContent className="p-0">
                  <div className="overflow-x-auto">
                    <table className="w-full text-sm">
                      <thead>
                        <tr className="border-b border-blue-600/30">
                          <th className="text-left text-blue-300 p-3 font-semibold">#</th>
                          <th className="text-left text-blue-300 p-3 font-semibold">Team</th>
                          <th className="text-center text-blue-300 p-3 font-semibold">MP</th>
                          <th className="text-center text-blue-300 p-3 font-semibold">W</th>
                          <th className="text-center text-blue-300 p-3 font-semibold">D</th>
                          <th className="text-center text-blue-300 p-3 font-semibold">L</th>
                          <th className="text-center text-blue-300 p-3 font-semibold">Pts</th>
                        </tr>
                      </thead>
                      <tbody>
                        {leagueTable.map((team, index) => (
                          <motion.tr
                            key={team.pos}
                            initial={{ opacity: 0, x: 10 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ duration: 0.4, delay: 0.6 + index * 0.05 }}
                            className="border-b border-blue-600/20 hover:bg-blue-700/30 transition-colors"
                          >
                            <td className="p-3 text-white font-semibold">{team.pos}</td>
                            <td className="p-3 text-white">{team.team}</td>
                            <td className="p-3 text-center text-blue-200">{team.mp}</td>
                            <td className="p-3 text-center text-green-400">{team.w}</td>
                            <td className="p-3 text-center text-yellow-400">{team.d}</td>
                            <td className="p-3 text-center text-red-400">{team.l}</td>
                            <td className="p-3 text-center text-white font-semibold">{team.pts}</td>
                          </motion.tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
    <Footer />
    </>
  );
};

export default Homepage;