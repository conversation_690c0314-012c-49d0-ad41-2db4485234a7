import Header from "@/components/Header";
import Footer from "@/components/Footer";
import Breadcrumb from "@/components/Breadcrumb";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Calendar, Clock, User } from "lucide-react";

const News = () => {
  const newsItems = [
    {
      id: 1,
      title: "Crown FC Secures Victory in Championship Match",
      excerpt: "An outstanding performance from the team led to a 3-1 victory against City United in yesterday's crucial championship match.",
      date: "2025-01-15",
      time: "14:30",
      author: "Sports Editor",
      category: "Match Report",
      image: "/placeholder-news1.jpg"
    },
    {
      id: 2,
      title: "New Signing: Welcome to Crown FC",
      excerpt: "We're excited to announce the signing of midfielder <PERSON>, who brings experience and skill to our squad.",
      date: "2025-01-14",
      time: "09:00",
      author: "Crown FC Media",
      category: "Transfer News",
      image: "/placeholder-news2.jpg"
    },
    {
      id: 3,
      title: "Youth Academy Hosts Training Camp",
      excerpt: "Our youth academy is hosting a special training camp for aspiring young players aged 12-16. Registration is now open.",
      date: "2025-01-13",
      time: "16:45",
      author: "Youth Coordinator",
      category: "Academy News",
      image: "/placeholder-news3.jpg"
    },
    {
      id: 4,
      title: "Match Preview: Crown FC vs Thunder FC",
      excerpt: "Everything you need to know about this weekend's crucial match against Thunder FC at our home ground.",
      date: "2025-01-12",
      time: "11:20",
      author: "Match Analyst",
      category: "Match Preview",
      image: "/placeholder-news4.jpg"
    }
  ];

  return (
    <>
      <Header />
      <Breadcrumb 
        items={[
          { label: "Home", href: "/" },
          { label: "News & Updates", href: "/news" }
        ]}
      />
      
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-4xl mx-auto">
          <h1 className="text-2xl md:text-3xl font-bold text-tm-navy mb-8">News & Updates</h1>
          
          {/* Featured News */}
          <Card className="mb-8">
            <CardHeader className="bg-tm-navy text-tm-white">
              <h2 className="font-bold">Latest News</h2>
            </CardHeader>
            <CardContent className="p-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <Badge variant="secondary" className="bg-tm-blue text-tm-white">
                    Match Report
                  </Badge>
                  <h3 className="text-xl font-bold text-tm-navy">
                    Crown FC Secures Victory in Championship Match
                  </h3>
                  <p className="text-tm-dark-gray">
                    An outstanding performance from the team led to a 3-1 victory against City United 
                    in yesterday's crucial championship match. The team showed great determination and skill.
                  </p>
                  <div className="flex items-center gap-4 text-sm text-tm-dark-gray">
                    <div className="flex items-center gap-1">
                      <Calendar className="h-4 w-4" />
                      <span>Jan 15, 2025</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <Clock className="h-4 w-4" />
                      <span>14:30</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <User className="h-4 w-4" />
                      <span>Sports Editor</span>
                    </div>
                  </div>
                </div>
                <div className="bg-tm-gray rounded-lg h-48 flex items-center justify-center">
                  <span className="text-tm-dark-gray">Match Highlights</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* News Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {newsItems.map((item) => (
              <Card key={item.id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="space-y-4">
                    <div className="bg-tm-gray rounded-lg h-32 flex items-center justify-center">
                      <span className="text-tm-dark-gray">News Image</span>
                    </div>
                    <div>
                      <Badge variant="outline" className="mb-2">
                        {item.category}
                      </Badge>
                      <h3 className="font-bold text-tm-navy mb-2 hover:text-tm-blue cursor-pointer">
                        {item.title}
                      </h3>
                      <p className="text-sm text-tm-dark-gray mb-4">
                        {item.excerpt}
                      </p>
                      <div className="flex items-center gap-4 text-xs text-tm-dark-gray">
                        <div className="flex items-center gap-1">
                          <Calendar className="h-3 w-3" />
                          <span>{new Date(item.date).toLocaleDateString()}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Clock className="h-3 w-3" />
                          <span>{item.time}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Press Releases */}
          <Card className="mt-8">
            <CardHeader className="bg-tm-blue text-tm-white">
              <h2 className="font-bold">Press Releases</h2>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-4">
                <div className="border-b border-tm-gray pb-4">
                  <h3 className="font-bold text-tm-navy mb-2">Crown FC Announces New Partnership</h3>
                  <p className="text-sm text-tm-dark-gray mb-2">
                    We're excited to announce a new partnership with local sports academy to enhance 
                    youth development programs.
                  </p>
                  <div className="flex items-center gap-4 text-xs text-tm-dark-gray">
                    <span>Jan 10, 2025</span>
                    <span>Official Statement</span>
                  </div>
                </div>
                <div className="border-b border-tm-gray pb-4">
                  <h3 className="font-bold text-tm-navy mb-2">Season Schedule Update</h3>
                  <p className="text-sm text-tm-dark-gray mb-2">
                    Important updates regarding upcoming match schedules and venue changes for the 
                    remainder of the season.
                  </p>
                  <div className="flex items-center gap-4 text-xs text-tm-dark-gray">
                    <span>Jan 8, 2025</span>
                    <span>Schedule Update</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      <Footer />
    </>
  );
};

export default News;